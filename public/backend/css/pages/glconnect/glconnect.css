.connect .card{background: #FFFFFF;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.04);
    border-radius: 6px;border: none;margin-top: 20px;
 }
 .connect .card .card-inner{padding: 20px;}
 .connect .card .card-inner h4{font-weight: 600;
     font-size: 16px;
     line-height: 24px;
     /* identical to box height */
     font-family: 'Poppins';
     letter-spacing: -0.25px;
     color: #3A3D4B;}
     .connect .card .card-inner p{font-family: 'Poppins';
     font-style: normal;
     font-weight: 400;
     font-size: 12px;
     line-height: 18px;
     color: rgba(58, 61, 75, 0.8);padding-top: 15px;
 }
 .connect .card .card-inner span{
     font-weight: 500;
     font-size: 12px;
     line-height: 18px;
     /* identical to box height */
     font-family: 'Poppins';
     color: rgba(58, 61, 75, 0.6);
 }
 .connect-logo{float: left;margin-right: 15px;}
 .lcs_wrap{width: 100px !important;}
 .lcs_label{width: auto !important;font-family: 'Poppins';}
 .lcs_switch.lcs_on .lcs_cursor {
 left: auto !important;
 right: 3px;
 }
 .lcs_switch.lcs_on {
 background: #FC5E6C !important;
 }
 .connect-banner{padding: 30px;}
 .connect-banner h3{font-family: 'Poppins';
     font-style: normal;
     font-weight: 600;
     font-size: 22px;
     line-height: 33px;
     /* identical to box height */
     letter-spacing: -0.25px;
     color: #FC5E6C;
 }
 .connect-banner p{
     font-family: 'Poppins';
     font-style: normal;
     font-weight: 400;
     font-size: 12px;
     line-height: 18px;
     color: rgba(58, 61, 75, 0.8);
 }
 .fl-left{float: left;}
 .configure{
     position: absolute;
     right: 20px;
     bottom: 24px;
     color: #FC5E6C;
     font-family: 'Poppins';
     font-style: normal;
     font-weight: 600;
     font-size: 12px;
     line-height: 33px;
     letter-spacing: -0.25px;
 }
 .configure:hover{
     color: #bd636bfd;
 }

 /* Button container for cards with multiple actions */
 .card-actions {
     position: absolute;
     bottom: 20px;
     right: 20px;
     left: 20px;
     display: flex;
     gap: 10px;
     flex-wrap: wrap;
     justify-content: center;
 }

 .card-actions .configure {
     position: relative;
     right: auto;
     bottom: auto;
     width: 120px;
     text-align: center;
     padding: 8px 12px;
     border: 1px solid #FC5E6C;
     border-radius: 4px;
     background: transparent;
     transition: all 0.3s ease;
     text-decoration: none;
     display: inline-block;
     line-height: 1.2;
 }

 .card-actions .configure:hover {
     background: #FC5E6C;
     color: #fff !important;
     text-decoration: none;
 }

 .card-actions .configure.primary {
     background: #FC5E6C;
     color: #fff !important;
 }

 .card-actions .configure.primary:hover {
     background: #bd636bfd;
 }

 .card-actions .configure.secondary {
     background: transparent;
     color: #FC5E6C;
     border-color: #FC5E6C;
 }

 .card-actions .configure.secondary:hover {
     background: #FC5E6C;
     color: #fff !important;
 }

 /* Adjust card padding to accommodate button container */
 .connect .card .card-inner.has-actions {
     padding-bottom: 70px;
 }